# CFW反射漏洞利用工具包

🚨 **警告：此工具包仅用于安全研究和授权渗透测试，请勿用于非法用途！**

## 📋 工具包概述

本工具包针对CFW云防火墙系统中的Java反射漏洞进行利用，模拟真实的APT攻击场景。

### 🎯 漏洞位置
1. **PermissionAspect.java:74** - `ReflectionUtils.makeAccessible(field)`
2. **CommonUtils.java:56** - `field.setAccessible(true)`

### 🔍 攻击原理
- 利用`setAccessible(true)`绕过Java访问控制
- 通过反射访问Spring容器中的敏感对象
- 提取数据库连接信息、配置数据等敏感信息
- 进行横向攻击和数据渗透

## 🛠️ 工具说明

### 1. ReflectionExploit.java
**主要反射漏洞利用工具**

```bash
# 编译
javac -cp ".:lib/*" ReflectionExploit.java

# 运行
java -cp ".:lib/*" hack.ReflectionExploit
```

**功能：**
- 构造恶意Spring上下文载荷
- 触发PermissionAspect和CommonUtils的反射检查
- 从错误响应中提取数据库连接信息
- 尝试Actuator端点获取配置信息

**攻击流程：**
1. 侦察和信息收集
2. 构造Spring上下文恶意载荷
3. 发送恶意请求触发反射漏洞
4. 分析响应提取敏感信息
5. 执行二次攻击

### 2. DatabaseDirectAttack.java
**数据库直连攻击工具**

```bash
# 编译
javac -cp ".:lib/*" DatabaseDirectAttack.java

# 运行
java -cp ".:lib/*" hack.DatabaseDirectAttack
```

**功能：**
- 解析从反射漏洞获得的数据库连接信息
- 尝试常见的默认凭据组合
- 直接连接数据库进行数据提取
- 执行权限提升和敏感数据搜索

**使用场景：**
- 基于ReflectionExploit获得的连接信息
- 当反射漏洞成功泄露数据库凭据时使用

### 3. CFWAttackOrchestrator.java
**综合攻击协调器**

```bash
# 编译
javac -cp ".:lib/*" CFWAttackOrchestrator.java

# 运行
java -cp ".:lib/*" hack.CFWAttackOrchestrator
```

**功能：**
- 协调多阶段攻击流程
- 模拟真实APT攻击场景
- 生成详细的攻击报告
- 提供完整的攻击时间线

**攻击阶段：**
1. 侦察和信息收集
2. 反射漏洞利用
3. 数据库直连攻击
4. 横向移动
5. 数据渗透

## 🚀 快速开始

### 环境准备
1. 确保CFW项目在`localhost:9900`运行
2. 准备MySQL JDBC驱动
3. 安装Java 11+

### 执行攻击
```bash
# 1. 进入Hack目录
cd Hack

# 2. 编译所有工具
javac -cp ".:../lib/*" *.java

# 3. 执行综合攻击（推荐）
java -cp ".:../lib/*" hack.CFWAttackOrchestrator

# 或者单独执行反射漏洞利用
java -cp ".:../lib/*" hack.ReflectionExploit
```

## 📊 攻击效果

### 预期结果
- ✅ 成功触发反射漏洞
- ✅ 获取数据库连接信息
- ✅ 直接访问MySQL数据库
- ✅ 提取用户和防火墙配置数据
- ✅ 访问Redis缓存服务
- ✅ 获取系统敏感配置

### 成功指标
1. **反射漏洞触发**：响应中包含`setAccessible`相关错误
2. **信息泄露**：获得数据库连接字符串
3. **权限提升**：成功连接数据库
4. **数据渗透**：提取敏感数据

## 🔍 技术细节

### 反射漏洞利用原理
```java
// 漏洞代码示例 (PermissionAspect.java)
Field[] fields = paramClazz.getDeclaredFields();
for (Field field : fields) {
    ReflectionUtils.makeAccessible(field); // 🚨 漏洞点
    Object value = field.get(param);
}

// 漏洞代码示例 (CommonUtils.java)
Field[] fields = clazz.getDeclaredFields();
for (Field field : fields) {
    field.setAccessible(true); // 🚨 漏洞点
    Object obj = field.get(object);
}
```

### 恶意载荷构造
```java
public class SpringContextPayload {
    public String cfwInstanceId; // 触发PermissionAspect检查
    
    // 私有字段用于模拟Spring容器敏感对象
    private String applicationContext;
    private String dataSource;
    private String redisTemplate;
    private String configurationProperties;
}
```

## 🛡️ 防护建议

### 立即修复
1. **限制setAccessible使用**
   ```java
   // 修复前
   field.setAccessible(true);
   
   // 修复后
   if (field.isAccessible() || isAllowedField(field)) {
       field.setAccessible(true);
   }
   ```

2. **实施字段访问白名单**
   ```java
   private static final Set<String> ALLOWED_FIELDS = Set.of(
       "cfwInstanceId", "publicField1", "publicField2"
   );
   
   if (!ALLOWED_FIELDS.contains(field.getName())) {
       continue; // 跳过不允许的字段
   }
   ```

3. **增强异常处理**
   ```java
   try {
       // 反射操作
   } catch (Exception e) {
       log.error("Reflection error", e); // 不要暴露详细信息给用户
       throw new BusinessException("操作失败");
   }
   ```

### 长期防护
- 实施代码审计流程
- 使用静态代码分析工具
- 定期安全测试
- 网络隔离和访问控制
- 敏感数据加密存储

## ⚠️ 免责声明

本工具包仅用于：
- 安全研究和学习
- 授权的渗透测试
- 漏洞验证和修复

**严禁用于：**
- 未授权的系统攻击
- 恶意数据窃取
- 任何违法犯罪活动

使用者需承担相应的法律责任！

## 📞 联系信息

如有技术问题或安全建议，请联系安全团队。

---

**记住：安全研究的目的是让系统更安全，而不是造成破坏！**
